<!DOCTYPE html>
<html>
<head>
    <title>Test Address Validation</title>
</head>
<body>
    <h1>Test Address Validation</h1>
    
    <div>
        <label>Enter an address:</label><br>
        <input type="text" id="address" placeholder="123 Main Street, Salt Lake City, UT 84101" style="width: 400px; padding: 8px;">
        <button onclick="testAddress()">Test</button>
    </div>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        async function testAddress() {
            const address = document.getElementById('address').value;
            const resultDiv = document.getElementById('result');
            
            if (!address) {
                resultDiv.innerHTML = 'Please enter an address';
                return;
            }
            
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/api/validate-address', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ address: address }),
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    resultDiv.innerHTML = `
                        <h3>Result:</h3>
                        <p><strong>Valid:</strong> ${data.isValid}</p>
                        <p><strong>Message:</strong> ${data.message}</p>
                        ${data.serviceArea ? `<p><strong>Service Area:</strong> ${data.serviceArea.name}, ${data.serviceArea.state}</p>` : ''}
                        ${data.suggestedArea ? `<p><strong>Suggested Area:</strong> ${data.suggestedArea}</p>` : ''}
                        ${data.coordinates ? `<p><strong>Coordinates:</strong> ${data.coordinates.lat}, ${data.coordinates.lng}</p>` : ''}
                    `;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">Error: ${result.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Test some addresses on load
        window.onload = function() {
            document.getElementById('address').value = '123 Main Street, Salt Lake City, UT 84101';
        }
    </script>
</body>
</html>
