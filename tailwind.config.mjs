/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  
  // Mobile-first approach - all utilities are mobile by default
  theme: {
    extend: {
      // Autumn Farmhouse Brand Colors
      colors: {
        primary: {
          50: '#fef7f0',
          100: '#fdeee0',
          200: '#fad5b8',
          300: '#f7bc90',
          400: '#f4a368',
          500: '#d2691e', // Warm Burnt Orange - main brand color
          600: '#b8591a',
          700: '#9e4916',
          800: '#843912',
          900: '#6a290e',
          950: '#50190a',
        },
        secondary: {
          50: '#f8f5f0',
          100: '#f1ebe0',
          200: '#e3d7c1',
          300: '#d5c3a2',
          400: '#c7af83',
          500: '#cd853f', // <PERSON> - complementary accent
          600: '#b8753a',
          700: '#a36535',
          800: '#8e5530',
          900: '#79452b',
          950: '#643526',
        },
        accent: {
          50: '#fefcf0',
          100: '#fdf9e0',
          200: '#fbf3c1',
          300: '#f9eda2',
          400: '#f7e783',
          500: '#b8860b', // Dark Goldenrod - harvest gold accent
          600: '#a6780a',
          700: '#946a09',
          800: '#825c08',
          900: '#704e07',
          950: '#5e4006',
        },
        dark: {
          50: '#f5f3f0',
          100: '#ebe7e0',
          200: '#d7cfc1',
          300: '#c3b7a2',
          400: '#af9f83',
          500: '#8b7355', // Warm Dark Gray
          600: '#7d6650',
          700: '#6f594b',
          800: '#614c46',
          900: '#533f41',
          950: '#3e2723', // Dark Brown - rich earth tone
        },
        darker: {
          50: '#f3f1ef',
          100: '#e7e3df',
          200: '#cfc7bf',
          300: '#b7ab9f',
          400: '#9f8f7f',
          500: '#6d5a47', // Rich Taupe
          600: '#625142',
          700: '#57483d',
          800: '#4c3f38',
          900: '#413633',
          950: '#2e1a16', // Deep Espresso - darkest brown
        },
        warm: {
          50: '#fefcf8', // Warm White
          100: '#faf7f2', // Warm Cream
          200: '#f5f2ed', // Warm Light Gray
          300: '#f5f5dc', // Beige - warm neutral
          400: '#ede7dd', // Soft Beige Gray
          500: '#d4c4b0', // Warm Medium Gray
          600: '#a68b5b', // Taupe
          700: '#9caf88', // Sage Green - muted natural green
          800: '#a0522d', // Sienna - rustic red-brown
          900: '#cd5c5c', // Indian Red - warm earthy red
          950: '#daa520', // Goldenrod - harvest yellow
        }
      },
      
      // Typography scale optimized for mobile readability
      fontFamily: {
        sans: ['Poppins', 'system-ui', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      // Spacing scale for consistent mobile-first design
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // Mobile-optimized breakpoints
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      
      // Animation and transitions optimized for mobile performance
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      
      // Box shadows optimized for mobile
      boxShadow: {
        'mobile': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'mobile-lg': '0 4px 16px rgba(0, 0, 0, 0.15)',
        'mobile-xl': '0 8px 32px rgba(0, 0, 0, 0.2)',
      },
      
      // Border radius scale
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // Z-index scale for layering
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    
    // Custom plugin for mobile-first utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        // Touch-friendly tap targets
        '.tap-target': {
          minHeight: '44px',
          minWidth: '44px',
        },
        
        // Safe area utilities for mobile devices
        '.safe-top': {
          paddingTop: 'env(safe-area-inset-top)',
        },
        '.safe-bottom': {
          paddingBottom: 'env(safe-area-inset-bottom)',
        },
        '.safe-left': {
          paddingLeft: 'env(safe-area-inset-left)',
        },
        '.safe-right': {
          paddingRight: 'env(safe-area-inset-right)',
        },
        
        // Mobile-optimized text shadows
        '.text-shadow-sm': {
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)',
        },
        '.text-shadow': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)',
        },
        '.text-shadow-lg': {
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.5)',
        },
        
        // Performance optimized transforms
        '.gpu-accelerated': {
          transform: 'translateZ(0)',
          willChange: 'transform',
        },
      }
      
      addUtilities(newUtilities)
    }
  ],
}
