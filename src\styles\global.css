/* Global styles for Mountain Porch Pumpkins - Mobile-first approach */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
    /* Prevent layout shifts */
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    font-family: 'Poppins', system-ui, sans-serif;
    line-height: 1.6;
    /* Performance optimizations */
    font-display: swap;
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
  
  /* Focus management for accessibility */
  :focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }
  
  :focus:not(:focus-visible) {
    outline: none;
  }
  
  /* Image optimization */
  img {
    max-width: 100%;
    height: auto;
    display: block;
    /* Performance optimization */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  /* Lazy loading images */
  img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  img[loading="lazy"].loaded,
  img.loaded {
    opacity: 1;
  }
}

/* Custom component styles */
@layer components {
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 tap-target;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 shadow-md hover:shadow-lg transform hover:scale-105;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-primary-500 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  /* Card components */
  .card {
    @apply bg-white rounded-xl shadow-lg overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-xl hover:scale-105 gpu-accelerated;
  }
  
  /* Form components */
  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors tap-target;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply text-red-600 text-sm mt-1;
  }

  /* Toggle button styles */
  .toggle-btn {
    @apply px-4 py-2 rounded-md text-sm font-semibold transition-all duration-200 text-gray-600 hover:text-primary-500 hover:bg-primary-50;
  }

  .toggle-btn.active {
    @apply bg-primary-500 text-white shadow-md;
  }

  /* Payment section styles */
  .payment-section {
    @apply p-6 bg-gradient-to-br from-primary-50 to-primary-100 border-2 border-primary-200 rounded-xl;
  }
  
  /* Navigation components */
  .nav-link {
    @apply text-gray-700 hover:text-primary-500 transition-colors duration-200 font-medium;
  }
  
  .nav-link-active {
    @apply nav-link text-primary-500;
  }
  
  /* Section components */
  .section {
    @apply py-16 lg:py-24;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-gray-900 mb-6;
  }
  
  .section-subtitle {
    @apply text-lg md:text-xl text-gray-600 max-w-3xl mx-auto;
  }
  
  /* Container components */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Modal components */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4;
  }
  
  .modal-content {
    @apply bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-screen overflow-y-auto;
  }
  
  /* Loading states */
  .loading {
    @apply opacity-70 pointer-events-none;
  }
  
  .spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Text shadows for better readability */
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }
  
  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Touch-friendly tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Aspect ratio utilities for images */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  .aspect-portrait {
    aspect-ratio: 3 / 4;
  }
  
  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.400') theme('colors.gray.100');
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: theme('colors.gray.100');
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: theme('colors.gray.400');
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: theme('colors.gray.500');
  }

  /* Enhanced Lazy Loading Utilities */
  .lazy-content {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .lazy-content.content-loaded {
    opacity: 1;
    transform: translateY(0);
  }

  .lazy-component {
    opacity: 0;
    transition: opacity 0.4s ease-out;
  }

  .lazy-component.component-loaded {
    opacity: 1;
  }

  /* Animation classes for content */
  .animate-in {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced lazy image loading */
  .lazy-image {
    background-color: theme('colors.gray.100');
    transition: opacity 0.3s ease-out;
  }

  .lazy-image.lazy-loaded {
    background-color: transparent;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  img {
    max-width: 100% !important;
    height: auto !important;
  }
}
