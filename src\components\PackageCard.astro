---
// Package Card component following SOLID principles
// Single Responsibility: Display package information and handle selection
// Open/Closed: Extensible through props and slots
// Interface Segregation: Clean package interface

import type { Package } from '../types';
import OptimizedImage from './OptimizedImage.astro';
import FeatureIcon from './FeatureIcon.astro';
import CategorizedFeatures from './CategorizedFeatures.astro';

export interface Props {
  package: Package;
  featured?: boolean;
  showModal?: boolean;
  class?: string;
}

const { 
  package: pkg, 
  featured = false, 
  showModal = true,
  class: className = ''
} = Astro.props;

// Format price for display
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(price);
};

// Get size badge color
const getSizeBadgeColor = (size: string) => {
  switch (size) {
    case 'small': return 'bg-green-100 text-green-800';
    case 'medium': return 'bg-blue-100 text-blue-800';
    case 'large': return 'bg-purple-100 text-purple-800';
    case 'custom': return 'bg-orange-100 text-orange-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};
---

<div 
  class:list={[
    'group relative bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:scale-105 gpu-accelerated cursor-pointer',
    featured && 'ring-2 ring-primary-500 ring-offset-2',
    className
  ]}
  x-data="packageSelector()"
  data-package-id={pkg.id}
>
  <!-- Featured Badge -->
  {featured && (
    <div class="absolute top-4 left-4 z-10">
      <span class="bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
        Most Popular
      </span>
    </div>
  )}
  
  <!-- Size Badge -->
  <div class="absolute top-4 right-4 z-10">
    <span class:list={[
      'px-3 py-1 rounded-full text-xs font-medium capitalize shadow-sm',
      getSizeBadgeColor(pkg.size)
    ]}>
      {pkg.size}
    </span>
  </div>
  
  <!-- Package Image -->
  <div class="relative h-48 sm:h-56 overflow-hidden">
    <OptimizedImage
      src={pkg.image}
      alt={`${pkg.name} - ${pkg.description} - Professional pumpkin display package by Mountain Porch Pumpkins serving Utah Idaho Wyoming`}
      width={600}
      height={400}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
      loading="lazy"
      placeholder="blur"
    />
    
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
    
    <!-- Price Overlay -->
    <div class="absolute bottom-4 left-4 right-4">
      <div class="flex items-end justify-between text-white">
        <div>
          <h3 class="text-xl sm:text-2xl font-bold text-shadow mb-1">
            {pkg.name}
          </h3>
          <p class="text-sm text-gray-200 text-shadow-sm">
            {pkg.category === 'residential' ? 'Residential' : 
             pkg.category === 'business' ? 'Business' : 'Event'}
          </p>
        </div>
        <div class="text-right">
          <div class="text-2xl sm:text-3xl font-bold text-primary-300 text-shadow">
            {pkg.price > 0 ? formatPrice(pkg.price) : 'Custom'}
          </div>
          {pkg.price > 0 && (
            <p class="text-xs text-gray-300 text-shadow-sm">
              Starting price
            </p>
          )}
        </div>
      </div>


    </div>
  </div>
  
  <!-- Package Content -->
  <div class="p-6">
    <!-- Description -->
    <p class="text-gray-600 mb-4 line-clamp-2">
      {pkg.description}
    </p>
    
    <!-- Features Preview -->
    <div class="mb-6">
      <h4 class="text-sm font-semibold text-gray-900 mb-2">Includes:</h4>
      <ul class="space-y-1">
        {pkg.features.slice(0, 3).map(feature => (
          <li class="flex items-center text-sm text-gray-600">
            <FeatureIcon feature={feature} class="mr-2" variant="small" />
            {feature}
          </li>
        ))}
        {pkg.features.length > 3 && (
          <li class="text-sm text-primary-600 font-medium">
            + {pkg.features.length - 3} more features
          </li>
        )}
      </ul>
    </div>
    
    <!-- Action Button -->
    <button
      class="w-full bg-primary-500 hover:bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold transition-all transform hover:scale-105 tap-target focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
      @click.stop={`window.selectPackageAndScroll('${pkg.id}')`}
    >
      {pkg.price > 0 ? 'Select Package' : 'Get Custom Quote'}
    </button>
  </div>
  
  <!-- Hover Effect -->
  <div class="absolute inset-0 bg-primary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
</div>

<!-- Package Modal -->
{showModal && (
  <div 
    x-show="modalOpen && selectedPackage?.id === '{pkg.id}'"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
    @click="handleModalClick($event)"
    @keydown.escape="closeModal()"
    style="display: none;"
  >
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-screen overflow-y-auto">
      <!-- Modal Header -->
      <div class="relative">
        <img 
          src={pkg.image} 
          alt={`${pkg.name} package display`}
          class="w-full h-48 object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        <div class="absolute bottom-4 left-6 right-6 text-white">
          <h2 class="text-2xl font-bold text-shadow mb-1">{pkg.name}</h2>
          <p class="text-primary-300 font-semibold text-shadow">
            {pkg.price > 0 ? formatPrice(pkg.price) : 'Custom Pricing'}
          </p>
        </div>
        <button 
          @click="closeModal()"
          class="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors tap-target"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <!-- Modal Content -->
      <div class="p-6">
        <!-- Description -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
          <p class="text-gray-600">{pkg.description}</p>
        </div>
        
        <!-- Features -->
        <div class="mb-6">
          <CategorizedFeatures
            categorizedFeatures={pkg.categorizedFeatures}
            fallbackFeatures={pkg.features}
            variant="modal"
          />
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
          <button
            class="flex-1 bg-primary-500 hover:bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold transition-colors tap-target"
            @click={`closeModal(); window.selectPackageAndScroll('${pkg.id}')`}
          >
            {pkg.price > 0 ? 'Book This Package' : 'Get Custom Quote'}
          </button>
          <button 
            @click="closeModal()"
            class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors tap-target"
          >
            Continue Browsing
          </button>
        </div>
      </div>
    </div>
  </div>
)}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
